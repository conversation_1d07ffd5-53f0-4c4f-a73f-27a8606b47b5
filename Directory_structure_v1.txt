ChatPlatformSolution/
│
├── .git/                                # Thư mục quản lý của Git
├── .gitignore                           # Các file/thư mục Git sẽ bỏ qua
├── ChatPlatform.sln                     # File Solution của Visual Studio
├── README.md                            # Thông tin tổng quan về dự án
│
├── src/                                 # Thư mục chứa toàn bộ mã nguồn
│   │
│   ├── chat-platform.desktop/           # Project WPF Client
│   │   ├── App.xaml                     # Định nghĩa ứng dụng WPF
│   │   ├── App.xaml.cs                  # Code-behind cho App.xaml
│   │   ├── MainWindow.xaml              # Cửa sổ chính của ứng dụng
│   │   ├── MainWindow.xaml.cs           # Code-behind cho MainWindow.xaml
│   │   ├── AssemblyInfo.cs              # (Thường trong Properties/AssemblyInfo.cs ở SDK-style projects)
│   │   ├── Assets/                      # Tài nguyên tĩnh
│   │   │   ├── Images/
│   │   │   │   ├── logo.png
│   │   │   │   └── default_avatar.png
│   │   │   └── Fonts/
│   │   │       └── CustomFont.ttf
│   │   ├── Converters/                  # Các IValueConverter
│   │   │   ├── BooleanToVisibilityConverter.cs
│   │   │   └── OnlineStatusToBrushConverter.cs
│   │   ├── Core/                        # Logic cốt lõi của client, ví dụ: quản lý trạng thái global
│   │   │   └── UserSessionService.cs
│   │   ├── Helpers/                     # Các lớp tiện ích
│   │   │   └── PasswordHelper.cs       # (Ví dụ: cho PasswordBox binding)
│   │   ├── Models/                      # Các model dùng riêng cho client (ít khi cần nếu DTOs đủ)
│   │   │   └── ClientSettings.cs
│   │   ├── Resources/                   # Resource Dictionaries
│   │   │   ├── Styles/
│   │   │   │   ├── _BaseStyles.xaml    # (Chứa MergedDictionaries)
│   │   │   │   ├── ButtonStyles.xaml
│   │   │   │   ├── TextBoxStyles.xaml
│   │   │   │   ├── ListViewStyles.xaml
│   │   │   │   └── ScrollBarStyles.xaml
│   │   │   └── Templates/
│   │   │       ├── ChatMessageTemplate.xaml
│   │   │       ├── ServerListItemTemplate.xaml
│   │   │       └── ChannelListItemTemplate.xaml
│   │   ├── Services/                    # Logic nghiệp vụ phía client
│   │   │   ├── IAuthService.cs
│   │   │   ├── AuthService.cs
│   │   │   ├── IServerService.cs
│   │   │   ├── ServerService.cs
│   │   │   ├── IChannelService.cs
│   │   │   ├── ChannelService.cs
│   │   │   ├── IMessageService.cs
│   │   │   ├── MessageService.cs
│   │   │   ├── ISignalRChatService.cs
│   │   │   ├── SignalRChatService.cs
│   │   │   ├── IAiChatClientService.cs
│   │   │   ├── AiChatClientService.cs
│   │   │   ├── INavigationService.cs   # (Nếu dùng custom navigation)
│   │   │   └── NavigationService.cs
│   │   │   └── DialogService.cs        # (Để hiển thị dialog/popup)
│   │   ├── ViewModels/                  # ViewModels cho MVVM
│   │   │   ├── Base/
│   │   │   │   └── ViewModelBase.cs    # (Lớp cơ sở cho ViewModel, implement INotifyPropertyChanged)
│   │   │   ├── LoginViewModel.cs
│   │   │   ├── RegisterViewModel.cs
│   │   │   ├── MainViewModel.cs        # (ViewModel cho MainWindow hoặc ShellView)
│   │   │   ├── ServerListViewModel.cs
│   │   │   ├── ChannelListViewModel.cs
│   │   │   ├── ChatAreaViewModel.cs
│   │   │   ├── UserListViewModel.cs
│   │   │   ├── UserProfileViewModel.cs
│   │   │   ├── SettingsViewModel.cs
│   │   │   └── AIChatViewModel.cs
│   │   ├── Views/                       # UserControls và Windows
│   │   │   ├── LoginView.xaml
│   │   │   ├── LoginView.xaml.cs
│   │   │   ├── RegisterView.xaml
│   │   │   ├── RegisterView.xaml.cs
│   │   │   ├── ShellView.xaml          # (Cửa sổ chính sau login, host các UserControl)
│   │   │   ├── ShellView.xaml.cs
│   │   │   ├── UserControls/
│   │   │   │   ├── ServerListView.xaml
│   │   │   │   ├── ServerListView.xaml.cs
│   │   │   │   ├── ChannelListView.xaml
│   │   │   │   ├── ChannelListView.xaml.cs
│   │   │   │   ├── ChatAreaView.xaml
│   │   │   │   ├── ChatAreaView.xaml.cs
│   │   │   │   ├── UserListView.xaml
│   │   │   │   ├── UserListView.xaml.cs
│   │   │   │   ├── AIChatView.xaml
│   │   │   │   └── AIChatView.xaml.cs
│   │   ├── Properties/
│   │   │   └── AssemblyInfo.cs
│   │   ├── chat-platform.desktop.csproj
│   │   └── app.config (nếu cần)
│   │
│   ├── chat-platform.domain/            # Project Domain
│   │   ├── Entities/
│   │   │   ├── User.cs
│   │   │   ├── Role.cs
│   │   │   ├── UserRole.cs             # (Bảng nối User-Role)
│   │   │   ├── Server.cs
│   │   │   ├── Channel.cs
│   │   │   ├── Message.cs
│   │   │   ├── UserServer.cs           # (Bảng nối User-Server)
│   │   │   ├── Friendship.cs           # (Quan hệ bạn bè)
│   │   │   └── AIConversationLog.cs    # (Nếu lưu log chat AI)
│   │   ├── Enums/
│   │   │   ├── UserStatus.cs
│   │   │   ├── ChannelType.cs
│   │   │   └── FriendshipStatus.cs
│   │   ├── Interfaces/                  # (Interfaces cho Domain Services hoặc Repositories nếu theo DDD chặt chẽ)
│   │   │   └── IPasswordHasher.cs      # (Ví dụ, nếu logic hash nằm ở Domain)
│   │   ├── chat-platform.domain.csproj
│   │   └── (Class1.cs - xóa đi)
│   │
│   ├── chat-platfrom.data/              # Project Data Access (Cân nhắc đổi tên thành chat-platform.data)
│   │   ├── AppDbContext.cs              # DbContext của EF Core
│   │   ├── Migrations/                  # Thư mục chứa migrations
│   │   │   └── _20250605_InitialCreate.cs # (Tên file migration ví dụ)
│   │   ├── Repositories/                # (Tùy chọn - Repository Pattern)
│   │   │   ├── Interfaces/
│   │   │   │   ├── IUserRepository.cs
│   │   │   │   ├── IServerRepository.cs
│   │   │   │   └── IGenericRepository.cs
│   │   │   └── Implementations/
│   │   │       ├── UserRepository.cs
│   │   │       ├── ServerRepository.cs
│   │   │       └── GenericRepository.cs
│   │   ├── Configurations/              # (Cấu hình Fluent API cho Entities, nếu không đặt trong OnModelCreating)
│   │   │   └── UserConfiguration.cs
│   │   ├── chat-platfrom.data.csproj
│   │   └── (Class1.cs - xóa đi)
│   │
│   ├── chat-platform.shared/            # Project Shared DTOs và code dùng chung
│   │   ├── DTOs/
│   │   │   ├── Auth/
│   │   │   │   ├── RegisterRequestDto.cs
│   │   │   │   ├── LoginRequestDto.cs
│   │   │   │   └── AuthResponseDto.cs
│   │   │   ├── Users/
│   │   │   │   ├── UserDto.cs
│   │   │   │   └── UserProfileDto.cs
│   │   │   ├── Servers/
│   │   │   │   ├── ServerDto.cs
│   │   │   │   ├── CreateServerDto.cs
│   │   │   │   └── ServerDetailsDto.cs
│   │   │   ├── Channels/
│   │   │   │   ├── ChannelDto.cs
│   │   │   │   └── CreateChannelDto.cs
│   │   │   ├── Messages/
│   │   │   │   ├── MessageDto.cs
│   │   │   │   ├── CreateMessageDto.cs
│   │   │   │   └── EditMessageDto.cs
│   │   │   ├── Friends/
│   │   │   │   ├── FriendRequestDto.cs
│   │   │   │   └── FriendshipDto.cs
│   │   │   └── AI/
│   │   │       ├── AIChatRequestDto.cs
│   │   │       └── AIChatResponseDto.cs
│   │   ├── Constants/
│   │   │   └── AppConstants.cs
│   │   ├── Exceptions/
│   │   │   ├── NotFoundException.cs
│   │   │   └── ValidationException.cs
│   │   ├── chat-platform.shared.csproj
│   │   └── (Class1.cs - xóa đi)
│   │
│   └── server/                          # Thư mục nhóm các project phía server
│       │
│       ├── chat-platform.server.AppHost/
│       │   ├── Program.cs
│       │   ├── appsettings.json
│       │   ├── appsettings.Development.json
│       │   ├── Properties/
│       │   │   └── launchSettings.json
│       │   └── chat-platform.server.AppHost.csproj
│       │
│       ├── chat-platform.server.ApiService/
│       │   ├── Program.cs                 # Cấu hình services, middleware, authentication, authorization
│       │   ├── appsettings.json
│       │   ├── appsettings.Development.json
│       │   ├── Controllers/
│       │   │   ├── AuthController.cs
│       │   │   ├── UsersController.cs
│       │   │   ├── ServersController.cs
│       │   │   ├── ChannelsController.cs
│       │   │   ├── MessagesController.cs
│       │   │   ├── FriendsController.cs
│       │   │   └── AIChatController.cs
│       │   ├── Hubs/
│       │   │   └── ChatHub.cs             # SignalR Hub cho real-time chat
│       │   ├── Services/                  # Logic nghiệp vụ backend
│       │   │   ├── Interfaces/
│   │   │   │   │   ├── IAuthService.cs
│   │   │   │   │   ├── IUserService.cs
│   │   │   │   │   ├── IServerService.cs
│   │   │   │   │   ├── IChannelService.cs
│   │   │   │   │   ├── IMessageService.cs
│   │   │   │   │   ├── IFriendshipService.cs
│   │   │   │   │   └── IAiService.cs
│   │   │   └── Implementations/
│   │   │       ├── AuthService.cs
│   │   │       ├── UserService.cs
│   │   │       ├── ServerService.cs
│   │   │       ├── ChannelService.cs
│   │   │       ├── MessageService.cs
│   │   │       ├── FriendshipService.cs
│   │   │       └── AiService.cs           # (Tương tác với dịch vụ AI ngoài)
│       │   ├── Middleware/                # (Custom middleware, ví dụ: error handling)
│       │   │   └── ErrorHandlingMiddleware.cs
│       │   ├── Properties/
│       │   │   └── launchSettings.json
│       │   └── chat-platform.server.ApiService.csproj
│       │
│       ├── chat-platform.server.Web/      # Project Blazor/Web Frontend (nếu dùng cho admin/dashboard)
│       │   ├── Program.cs
│       │   ├── WeatherApiClient.cs        # (File ví dụ, có thể xóa/thay thế)
│       │   ├── Components/
│       │   │   ├── Layout/
│       │   │   │   ├── MainLayout.razor
│       │   │   │   ├── MainLayout.razor.css
│       │   │   │   ├── NavMenu.razor
│       │   │   │   └── NavMenu.razor.css
│       │   │   └── Pages/
│       │   │       ├── Index.razor
│       │   │       ├── Weather.razor
│       │   │       └── Error.razor
│       │   ├── wwwroot/
│       │   │   ├── app.css
│       │   │   └── bootstrap/
│       │   │       └── bootstrap.min.css
│       │   ├── Properties/
│       │   │   └── launchSettings.json
│       │   └── chat-platform.server.Web.csproj
│       │
│       └── chat-platform.server.ServiceDefaults/
│           ├── Extensions.cs
│           └── chat-platform.server.ServiceDefaults.csproj
│
├── tests/                                 # Thư mục chứa các project test
│   │
│   ├── chat-platform.server.Tests/
│   │   ├── WebTests.cs
│   │   ├── ApiServiceTests/
│   │   │   ├── AuthControllerTests.cs
│   │   │   └── ServersControllerTests.cs
│   │   ├── HubsTests/
│   │   │   └── ChatHubTests.cs
│   │   └── chat-platform.server.Tests.csproj
│   │
│   ├── ChatPlatform.Domain.Tests/         # (Tạo mới nếu cần test logic Domain)
│   │   └── UserEntityTests.cs
│   │   └── ChatPlatform.Domain.Tests.csproj
│   │
│   └── ChatPlatform.Desktop.Tests/      # (Tạo mới nếu cần test client WPF)
│       ├── ViewModelTests/
│       │   ├── LoginViewModelTests.cs
│       │   └── MainViewModelTests.cs
│       └── ChatPlatform.Desktop.Tests.csproj
│
└── (Các file khác ở root solution như global.json nếu có)