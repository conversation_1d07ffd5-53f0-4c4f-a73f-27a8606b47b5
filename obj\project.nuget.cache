{"version": 2, "dgSpecHash": "MCvBbEjjxWM=", "success": true, "projectFilePath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.desktop.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\controlzex\\4.4.0\\controlzex.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dialogboxformaterialdesigntoolkitinxaml\\1.0.0\\dialogboxformaterialdesigntoolkitinxaml.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dynamitey\\2.0.10.189\\dynamitey.2.0.10.189.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\extendedwpfconverters\\2.0.4\\extendedwpfconverters.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\extendedwpfvisualtreehelper\\2.0.3\\extendedwpfvisualtreehelper.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\handycontrol\\3.5.1\\handycontrol.3.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro\\2.4.10\\mahapps.metro.2.4.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesigncolors\\2.0.6\\materialdesigncolors.2.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesigninxamltoolkitaddons\\0.0.63\\materialdesigninxamltoolkitaddons.0.0.63.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesignthemes\\4.5.0\\materialdesignthemes.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.6.0\\microsoft.csharp.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\9.0.0\\microsoft.win32.systemevents.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.19\\microsoft.xaml.behaviors.wpf.1.1.19.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\9.0.0\\system.drawing.common.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'DialogBoxForMaterialDesignToolkitInXaml 1.0.0' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "DialogBoxForMaterialDesignToolkitInXaml", "targetGraphs": ["net8.0-windows7.0"]}]}