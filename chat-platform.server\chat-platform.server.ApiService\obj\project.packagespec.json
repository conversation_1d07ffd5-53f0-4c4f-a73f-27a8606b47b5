﻿"restore":{"projectUniqueName":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\chat-platform.server.ApiService.csproj","projectName":"chat-platform.server.ApiService","projectPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\chat-platform.server.ApiService.csproj","outputPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net8.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj":{"projectPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0":{"targetAlias":"net8.0","imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}