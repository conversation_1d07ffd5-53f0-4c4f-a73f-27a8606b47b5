﻿"restore":{"projectUniqueName":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj","projectName":"chat-platform.server.ServiceDefaults","projectPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj","outputPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net8.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"Microsoft.Extensions.Http.Resilience":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.ServiceDiscovery":{"target":"Package","version":"[8.2.2, )"},"OpenTelemetry.Exporter.OpenTelemetryProtocol":{"target":"Package","version":"[1.9.0, )"},"OpenTelemetry.Extensions.Hosting":{"target":"Package","version":"[1.9.0, )"},"OpenTelemetry.Instrumentation.AspNetCore":{"target":"Package","version":"[1.9.0, )"},"OpenTelemetry.Instrumentation.Http":{"target":"Package","version":"[1.9.0, )"},"OpenTelemetry.Instrumentation.Runtime":{"target":"Package","version":"[1.9.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}