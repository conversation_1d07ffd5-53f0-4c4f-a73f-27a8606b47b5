﻿"restore":{"projectUniqueName":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Tests\\chat-platform.server.Tests.csproj","projectName":"chat-platform.server.Tests","projectPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Tests\\chat-platform.server.Tests.csproj","outputPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Tests\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net8.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.AppHost\\chat-platform.server.AppHost.csproj":{"projectPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.AppHost\\chat-platform.server.AppHost.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"Aspire.Hosting.Testing":{"target":"Package","version":"[8.2.2, )"},"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.10.0, )"},"NUnit":{"target":"Package","version":"[4.1.0, )"},"NUnit.Analyzers":{"target":"Package","version":"[4.2.0, )"},"NUnit3TestAdapter":{"target":"Package","version":"[4.5.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}