// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class chat_platform_server_Tests
{
    private chat_platform_server_Tests() { }
    public static string ProjectPath => """D:\FPTU\StudyMaterials\Summer 25\PRN212\Project\chat-platform\chat-platform.server\chat-platform.server.Tests""";
}
