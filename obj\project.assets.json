{"version": 3, "targets": {"net8.0-windows7.0": {"ControlzEx/4.4.0": {"type": "package", "dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.19", "System.Text.Json": "4.7.2"}, "compile": {"lib/netcoreapp3.1/ControlzEx.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/ControlzEx.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "DialogBoxForMaterialDesignToolkitInXaml/1.0.0": {"type": "package", "dependencies": {"MaterialDesignThemes": "2.3.1.953"}, "compile": {"lib/net45/ExtraTools.dll": {}}, "runtime": {"lib/net45/ExtraTools.dll": {}}}, "Dynamitey/2.0.10.189": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.6.0", "System.ComponentModel": "4.3.0"}, "compile": {"lib/netstandard2.0/Dynamitey.dll": {}}, "runtime": {"lib/netstandard2.0/Dynamitey.dll": {}}}, "ExtendedWPFConverters/2.0.4": {"type": "package", "dependencies": {"System.Drawing.Common": "9.0.0"}, "compile": {"lib/net8.0-windows7.0/ExtendedWPFConverters.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/ExtendedWPFConverters.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "ExtendedWPFVisualTreeHelper/2.0.3": {"type": "package", "compile": {"lib/net8.0-windows7.0/ExtendedWPFVisualTreeHelper.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/ExtendedWPFVisualTreeHelper.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "HandyControl/3.5.1": {"type": "package", "compile": {"lib/net8.0/HandyControl.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/net8.0/HandyControl.dll": {"related": ".deps.json;.xml"}}}, "MahApps.Metro/2.4.10": {"type": "package", "dependencies": {"ControlzEx": "[4.4.0, 6.0.0)"}, "compile": {"lib/netcoreapp3.1/MahApps.Metro.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/MahApps.Metro.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "resource": {"lib/netcoreapp3.1/de/MahApps.Metro.resources.dll": {"locale": "de"}}}, "MaterialDesignColors/2.0.6": {"type": "package", "compile": {"lib/netcoreapp3.1/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/netcoreapp3.1/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignInXamlToolkitAddOns/0.0.63": {"type": "package", "dependencies": {"Dynamitey": "2.0.10.189", "ExtendedWPFConverters": "2.0.4", "ExtendedWPFVisualTreeHelper": "2.0.3", "MaterialDesignThemes": "4.5.0"}, "compile": {"lib/net8.0-windows7.0/MaterialDesignThemes.Wpf.AddOns.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MaterialDesignThemes.Wpf.AddOns.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MaterialDesignThemes/4.5.0": {"type": "package", "dependencies": {"MaterialDesignColors": "[2.0.6, 3.0.0)"}, "compile": {"lib/netcoreapp3.1/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.CSharp/4.6.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.19": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "System.ComponentModel/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {}}}, "System.Drawing.Common/9.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "9.0.0"}, "compile": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net8.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net8.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "compile": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}}}}, "libraries": {"ControlzEx/4.4.0": {"sha512": "pZ5z4hYWwE4R13UMCVs6vII//nL7hz+Nwn4oJlnsZJRGqJNy6Z9KnJiTZfly6lKFu0pMc1aWBZpx+VqFTQKP1Q==", "type": "package", "path": "controlzex/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "controlzex.4.4.0.nupkg.sha512", "controlzex.nuspec", "lib/net45/ControlzEx.dll", "lib/net45/ControlzEx.pdb", "lib/net45/ControlzEx.xml", "lib/net462/ControlzEx.dll", "lib/net462/ControlzEx.pdb", "lib/net462/ControlzEx.xml", "lib/netcoreapp3.0/ControlzEx.dll", "lib/netcoreapp3.0/ControlzEx.pdb", "lib/netcoreapp3.0/ControlzEx.xml", "lib/netcoreapp3.1/ControlzEx.dll", "lib/netcoreapp3.1/ControlzEx.pdb", "lib/netcoreapp3.1/ControlzEx.xml", "logo-mini.png"]}, "DialogBoxForMaterialDesignToolkitInXaml/1.0.0": {"sha512": "K9VIugj/E1KRuxaNcQrfCPWiADKmJGQo0zlx6hJ5hKHIMp6uzlFn7WBU/XOPzYh/F/r+JLR+9fKQwl32+zHSqw==", "type": "package", "path": "dialogboxformaterialdesigntoolkitinxaml/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dialogboxformaterialdesigntoolkitinxaml.1.0.0.nupkg.sha512", "dialogboxformaterialdesigntoolkitinxaml.nuspec", "lib/net45/ExtraTools.dll"]}, "Dynamitey/2.0.10.189": {"sha512": "sKo8BlCgQTOSFFK4RlMvC8abp0NqZrTd017fishVrkoqggNBpwa4RGq88wPjyedk5GjTWeB8/kGrbXES+Rcl7Q==", "type": "package", "path": "dynamitey/2.0.10.189", "files": [".nupkg.metadata", ".signature.p7s", "dynamitey.2.0.10.189.nupkg.sha512", "dynamitey.nuspec", "lib/net40/Dynamitey.dll", "lib/netstandard1.5/Dynamitey.dll", "lib/netstandard2.0/Dynamitey.dll", "lib/portable-net45+sl5+win8+wp8/Dynamitey.dll"]}, "ExtendedWPFConverters/2.0.4": {"sha512": "0PVuEdnTWFNt3Srs9N4erZRf/9NBVRkuXi80CA83dE3AZ6cbsC+fv29MDShR1B6YkjUJPHNz7t5/0HJpWWlACg==", "type": "package", "path": "extendedwpfconverters/2.0.4", "files": [".nupkg.metadata", ".signature.p7s", "extendedwpfconverters.2.0.4.nupkg.sha512", "extendedwpfconverters.nuspec", "lib/net471/ExtendedWPFConverters.dll", "lib/net471/ExtendedWPFConverters.xml", "lib/net8.0-windows7.0/ExtendedWPFConverters.dll", "lib/net8.0-windows7.0/ExtendedWPFConverters.xml", "lib/net9.0-windows7.0/ExtendedWPFConverters.dll", "lib/net9.0-windows7.0/ExtendedWPFConverters.xml"]}, "ExtendedWPFVisualTreeHelper/2.0.3": {"sha512": "3E4TJkFDpTFCpBHfQa7HQS1uyLBwpSlZMYCU1im1AnQLbYZIEmhnlulvlYezS8k3sY31f0uWHwuQBeEPle3kEQ==", "type": "package", "path": "extendedwpfvisualtreehelper/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "extendedwpfvisualtreehelper.2.0.3.nupkg.sha512", "extendedwpfvisualtreehelper.nuspec", "lib/net451/ExtendedWPFVisualTreeHelper.dll", "lib/net451/ExtendedWPFVisualTreeHelper.xml", "lib/net8.0-windows7.0/ExtendedWPFVisualTreeHelper.dll", "lib/net8.0-windows7.0/ExtendedWPFVisualTreeHelper.xml", "lib/net9.0-windows7.0/ExtendedWPFVisualTreeHelper.dll", "lib/net9.0-windows7.0/ExtendedWPFVisualTreeHelper.xml"]}, "HandyControl/3.5.1": {"sha512": "i2i0xrLev7F1MFnhf0DP1CNCdGw8hVJ0HJrI0kxRv02ZJgaAzLzDTgAXDPY8GAoD3aYnBLSgM74lBHZ844KQnQ==", "type": "package", "path": "handycontrol/3.5.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "handycontrol.3.5.1.nupkg.sha512", "handycontrol.nuspec", "icon.png", "lib/net40/HandyControl.dll", "lib/net40/HandyControl.xml", "lib/net45/HandyControl.dll", "lib/net45/HandyControl.xml", "lib/net451/HandyControl.dll", "lib/net451/HandyControl.xml", "lib/net452/HandyControl.dll", "lib/net452/HandyControl.xml", "lib/net46/HandyControl.dll", "lib/net46/HandyControl.xml", "lib/net461/HandyControl.dll", "lib/net461/HandyControl.xml", "lib/net462/HandyControl.dll", "lib/net462/HandyControl.xml", "lib/net47/HandyControl.dll", "lib/net47/HandyControl.xml", "lib/net471/HandyControl.dll", "lib/net471/HandyControl.xml", "lib/net472/HandyControl.dll", "lib/net472/HandyControl.xml", "lib/net48/HandyControl.dll", "lib/net48/HandyControl.xml", "lib/net481/HandyControl.dll", "lib/net481/HandyControl.xml", "lib/net5.0/HandyControl.deps.json", "lib/net5.0/HandyControl.dll", "lib/net5.0/HandyControl.xml", "lib/net6.0/HandyControl.deps.json", "lib/net6.0/HandyControl.dll", "lib/net6.0/HandyControl.xml", "lib/net7.0/HandyControl.deps.json", "lib/net7.0/HandyControl.dll", "lib/net7.0/HandyControl.xml", "lib/net8.0/HandyControl.deps.json", "lib/net8.0/HandyControl.dll", "lib/net8.0/HandyControl.xml", "lib/netcoreapp3.0/HandyControl.deps.json", "lib/netcoreapp3.0/HandyControl.dll", "lib/netcoreapp3.0/HandyControl.xml", "lib/netcoreapp3.1/HandyControl.deps.json", "lib/netcoreapp3.1/HandyControl.dll", "lib/netcoreapp3.1/HandyControl.xml", "tools/VisualStudioToolsManifest.xml"]}, "MahApps.Metro/2.4.10": {"sha512": "45exHKJCVYaD1/rNr3ekZPECEBM4uHOt6aYp6yNaJbliFMUo+d3z8Gi1xG+qEkbiHKITX+dlz+BW1FOsjAbl/w==", "type": "package", "path": "mahapps.metro/2.4.10", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/MahApps.Metro.dll", "lib/net452/MahApps.Metro.pdb", "lib/net452/MahApps.Metro.xml", "lib/net452/de/MahApps.Metro.resources.dll", "lib/net46/MahApps.Metro.dll", "lib/net46/MahApps.Metro.pdb", "lib/net46/MahApps.Metro.xml", "lib/net46/de/MahApps.Metro.resources.dll", "lib/net47/MahApps.Metro.dll", "lib/net47/MahApps.Metro.pdb", "lib/net47/MahApps.Metro.xml", "lib/net47/de/MahApps.Metro.resources.dll", "lib/netcoreapp3.0/MahApps.Metro.dll", "lib/netcoreapp3.0/MahApps.Metro.pdb", "lib/netcoreapp3.0/MahApps.Metro.xml", "lib/netcoreapp3.0/de/MahApps.Metro.resources.dll", "lib/netcoreapp3.1/MahApps.Metro.dll", "lib/netcoreapp3.1/MahApps.Metro.pdb", "lib/netcoreapp3.1/MahApps.Metro.xml", "lib/netcoreapp3.1/de/MahApps.Metro.resources.dll", "mahapps.metro.2.4.10.nupkg.sha512", "mahapps.metro.logo.png", "mahapps.metro.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "MaterialDesignColors/2.0.6": {"sha512": "eZNiLlMy7Ag9f3MnhVxSzkG2IIGCaRfqZKt6npEcEjLQ9kSgCYO5oUd8AgJjhpfCKwyf+GO5VDc5R88QSDTHHg==", "type": "package", "path": "materialdesigncolors/2.0.6", "files": [".nupkg.metadata", ".signature.p7s", "images/MaterialDesignColors.Icon.png", "lib/net452/MaterialDesignColors.dll", "lib/net452/MaterialDesignColors.pdb", "lib/netcoreapp3.1/MaterialDesignColors.dll", "lib/netcoreapp3.1/MaterialDesignColors.pdb", "materialdesigncolors.2.0.6.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignInXamlToolkitAddOns/0.0.63": {"sha512": "vFI+IYMuxfJf5qW8BK7XUcmtugn8nGvwqL1QxVTzAoBxdxa+aWXOJwpqLXo9DCEvJ5jiKKijzYXT9We8Ds9FdA==", "type": "package", "path": "materialdesigninxamltoolkitaddons/0.0.63", "files": [".nupkg.metadata", ".signature.p7s", "lib/net471/MaterialDesignThemes.Wpf.AddOns.dll", "lib/net471/MaterialDesignThemes.Wpf.AddOns.xml", "lib/net8.0-windows7.0/MaterialDesignThemes.Wpf.AddOns.dll", "lib/net8.0-windows7.0/MaterialDesignThemes.Wpf.AddOns.xml", "lib/net9.0-windows7.0/MaterialDesignThemes.Wpf.AddOns.dll", "lib/net9.0-windows7.0/MaterialDesignThemes.Wpf.AddOns.xml", "materialdesigninxamltoolkitaddons.0.0.63.nupkg.sha512", "materialdesigninxamltoolkitaddons.nuspec"]}, "MaterialDesignThemes/4.5.0": {"sha512": "G26hIGFwGH5q46CD59zGiAs4aLsASsKCCBqgnISPwIcq8pAfKRRAKxXfOQJgNDyIUnVm5Ug67gldsEgeWP6GoQ==", "type": "package", "path": "materialdesignthemes/4.5.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "images/MaterialDesignThemes.Icon.png", "lib/net452/MaterialDesignThemes.Wpf.dll", "lib/net452/MaterialDesignThemes.Wpf.pdb", "lib/net452/MaterialDesignThemes.Wpf.xml", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.dll", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.pdb", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.4.5.0.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.CSharp/4.6.0": {"sha512": "kxn3M2rnAGy5N5DgcIwcE8QTePWU/XiYcQVzn9HqTls2NKluVzVSmVWRjK7OUPWbljCXuZxHyhEz9kPRIQeXow==", "type": "package", "path": "microsoft.csharp/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.6.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.SystemEvents/9.0.0": {"sha512": "z8FfGIaoeALdD+KF44A2uP8PZIQQtDGiXsOLuN8nohbKhkyKt7zGaZb+fKiCxTuBqG22Q7myIAioSWaIcOOrOw==", "type": "package", "path": "microsoft.win32.systemevents/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/net9.0/Microsoft.Win32.SystemEvents.dll", "lib/net9.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.9.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.19": {"sha512": "5sPWkbqImc2t1aQwIfJcKsUo7tOg1Tr8+6xVzZJB56Nzt4u9NlpcLofgdX/aRYpPKdWDA3U23Akw1KQzU5e82g==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.19", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.19.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "System.ComponentModel/4.3.0": {"sha512": "VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "type": "package", "path": "system.componentmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.4.3.0.nupkg.sha512", "system.componentmodel.nuspec"]}, "System.Drawing.Common/9.0.0": {"sha512": "uoozjI3+dlgKh2onFJcz8aNLh6TRCPlLSh8Dbuljc8CdvqXrxHOVysJlrHvlsOCqceqGBR1wrMPxlnzzhynktw==", "type": "package", "path": "system.drawing.common/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/net8.0/System.Private.Windows.Core.dll", "lib/net8.0/System.Private.Windows.Core.xml", "lib/net9.0/System.Drawing.Common.dll", "lib/net9.0/System.Drawing.Common.pdb", "lib/net9.0/System.Drawing.Common.xml", "lib/net9.0/System.Private.Windows.Core.dll", "lib/net9.0/System.Private.Windows.Core.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.9.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Text.Json/4.7.2": {"sha512": "TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "type": "package", "path": "system.text.json/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.4.7.2.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["DialogBoxForMaterialDesignToolkitInXaml >= 1.0.0", "HandyControl >= 3.5.1", "MahApps.Metro >= 2.4.10", "MaterialDesignInXamlToolkitAddOns >= 0.0.63"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.desktop.csproj", "projectName": "chat-platform.desktop", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.desktop.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"DialogBoxForMaterialDesignToolkitInXaml": {"target": "Package", "version": "[1.0.0, )"}, "HandyControl": {"target": "Package", "version": "[3.5.1, )"}, "MahApps.Metro": {"target": "Package", "version": "[2.4.10, )"}, "MaterialDesignInXamlToolkitAddOns": {"target": "Package", "version": "[0.0.63, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'DialogBoxForMaterialDesignToolkitInXaml 1.0.0' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "DialogBoxForMaterialDesignToolkitInXaml", "targetGraphs": ["net8.0-windows7.0"]}]}