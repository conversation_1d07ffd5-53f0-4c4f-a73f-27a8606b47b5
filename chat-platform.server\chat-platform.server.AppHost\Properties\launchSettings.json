{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:17253;http://localhost:15054", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "https://localhost:21220", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "https://localhost:22266"}}, "http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:15054", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "http://localhost:19145", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "http://localhost:20270"}}}}