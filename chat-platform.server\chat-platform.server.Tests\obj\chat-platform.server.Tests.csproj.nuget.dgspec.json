{"format": 1, "restore": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Tests\\chat-platform.server.Tests.csproj": {}}, "projects": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\chat-platform.server.ApiService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\chat-platform.server.ApiService.csproj", "projectName": "chat-platform.server.ApiService", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\chat-platform.server.ApiService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj": {"projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.AppHost\\chat-platform.server.AppHost.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.AppHost\\chat-platform.server.AppHost.csproj", "projectName": "chat-platform.server.AppHost", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.AppHost\\chat-platform.server.AppHost.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.AppHost\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\chat-platform.server.ApiService.csproj": {"projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ApiService\\chat-platform.server.ApiService.csproj"}, "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Web\\chat-platform.server.Web.csproj": {"projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Web\\chat-platform.server.Web.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Aspire.Hosting": {"target": "Package", "version": "[8.2.2, )"}, "Aspire.Hosting.AppHost": {"target": "Package", "version": "[8.2.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj", "projectName": "chat-platform.server.ServiceDefaults", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Http.Resilience": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.ServiceDiscovery": {"target": "Package", "version": "[8.2.2, )"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol": {"target": "Package", "version": "[1.9.0, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.9.0, )"}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.9.0, )"}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.9.0, )"}, "OpenTelemetry.Instrumentation.Runtime": {"target": "Package", "version": "[1.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Tests\\chat-platform.server.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Tests\\chat-platform.server.Tests.csproj", "projectName": "chat-platform.server.Tests", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Tests\\chat-platform.server.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Tests\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.AppHost\\chat-platform.server.AppHost.csproj": {"projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.AppHost\\chat-platform.server.AppHost.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Aspire.Hosting.Testing": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.10.0, )"}, "NUnit": {"target": "Package", "version": "[4.1.0, )"}, "NUnit.Analyzers": {"target": "Package", "version": "[4.2.0, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Web\\chat-platform.server.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Web\\chat-platform.server.Web.csproj", "projectName": "chat-platform.server.Web", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Web\\chat-platform.server.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.Web\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj": {"projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Project\\chat-platform\\chat-platform.server\\chat-platform.server.ServiceDefaults\\chat-platform.server.ServiceDefaults.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}