{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "weatherforecast", "applicationUrl": "http://localhost:5562", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "weatherforecast", "applicationUrl": "https://localhost:7398;http://localhost:5562", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}